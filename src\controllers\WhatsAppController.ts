import { Request, Response } from 'express';
import { config } from '@config/environment';
import { AIService } from '@services/aiService';
import { VectorSearchService } from '@services/vectorSearchService';
import { WhatsAppWebhookPayload, WhatsAppMessage, ChatMessage } from '@/types/common';
import { v4 as uuidv4 } from 'uuid';

/**
 * WhatsApp Controller
 * Handles WhatsApp webhook verification and message processing with AI integration
 */
export class WhatsAppController {
  private aiService: AIService;
  private vectorSearchService: VectorSearchService;
  private conversationHistory: Map<string, ChatMessage[]> = new Map();

  constructor() {
    this.aiService = new AIService();
    this.vectorSearchService = new VectorSearchService();
  }
  /**
   * Verify WhatsApp webhook
   * GET /api/whatsapp/webhook
   */
  public verifyWebhook = (req: Request, res: Response): void => {
    try {
      const { 'hub.mode': mode, 'hub.challenge': challenge, 'hub.verify_token': token } = req.query;

      // Check if mode and token are valid
      if (mode === 'subscribe' && token === config.whatsapp.verifyToken) {
        console.log('WEBHOOK VERIFIED');
        res.status(200).send(challenge);
      } else {
        console.log('Failed verification. Make sure the verify tokens match.');
        res.status(403).end();
      }
    } catch (error) {
      console.error('Error verifying webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during webhook verification',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Handle incoming WhatsApp messages with AI processing
   * POST /api/whatsapp/webhook
   */
  public handleWebhook = async (req: Request, res: Response): Promise<void> => {
    try {
      const webhookData: WhatsAppWebhookPayload = req.body;

      console.log('Received WhatsApp webhook:', JSON.stringify(webhookData, null, 2));

      // Process each entry in the webhook
      for (const entry of webhookData.entry) {
        for (const change of entry.changes) {
          if (change.value.messages) {
            for (const message of change.value.messages) {
              await this.processMessage(message, change.value.metadata.phone_number_id);
            }
          }
        }
      }

      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully'
      });
    } catch (error) {
      console.error('Error handling WhatsApp webhook:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during webhook handling',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Process individual WhatsApp message with AI
   */
  private async processMessage(message: WhatsAppMessage, phoneNumberId: string): Promise<void> {
    try {
      // Only process text messages for now
      if (message.type !== 'text' || !message.text?.body) {
        console.log(`Skipping non-text message of type: ${message.type}`);
        return;
      }

      const userMessage = message.text.body;
      const phoneNumber = message.from;

      console.log(`Processing message from ${phoneNumber}: ${userMessage}`);

      // Get or create conversation history for this phone number
      let history = this.conversationHistory.get(phoneNumber) || [];

      // Add user message to history
      const chatMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content: userMessage,
        timestamp: new Date(),
        metadata: {
          whatsappMessageId: message.id,
          phoneNumber: phoneNumber
        }
      };

      history.push(chatMessage);

      // Search for relevant data
      const searchResults = await this.vectorSearchService.search(userMessage);
      const relevantData = searchResults.map((result) => result.item);

      // Generate AI response
      const aiResponseText = await this.aiService.generateResponse(
        userMessage,
        relevantData,
        history.slice(-10) // Last 10 messages for context
      );

      // Create assistant message
      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: aiResponseText,
        timestamp: new Date(),
        metadata: {
          relevantDataCount: relevantData.length,
          searchResultsCount: searchResults.length
        }
      };

      history.push(assistantMessage);

      // Keep only last 20 messages to prevent memory issues
      if (history.length > 20) {
        history = history.slice(-20);
      }

      this.conversationHistory.set(phoneNumber, history);

      // Send response back to WhatsApp
      await this.sendWhatsAppMessage(phoneNumber, aiResponseText, phoneNumberId);

    } catch (error) {
      console.error('Error processing WhatsApp message:', error);

      // Send error message to user
      const errorMessage = "I'm sorry, I'm having trouble processing your message right now. Please try again later.";
      try {
        await this.sendWhatsAppMessage(message.from, errorMessage, phoneNumberId);
      } catch (sendError) {
        console.error('Error sending error message:', sendError);
      }
    }
  }

  /**
   * Send message back to WhatsApp user
   */
  private async sendWhatsAppMessage(to: string, message: string, phoneNumberId: string): Promise<void> {
    try {
      if (!config.whatsapp.accessToken) {
        console.error('WhatsApp access token not configured');
        return;
      }

      const url = `https://graph.facebook.com/v18.0/${phoneNumberId}/messages`;

      const payload = {
        messaging_product: 'whatsapp',
        to: to,
        type: 'text',
        text: {
          body: message
        }
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.whatsapp.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`WhatsApp API error: ${response.status} - ${errorData}`);
      }

      const responseData = await response.json();
      console.log('Message sent successfully:', responseData);

    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Get conversation history for a phone number
   * GET /api/whatsapp/history/:phoneNumber
   */
  public getConversationHistory = (req: Request, res: Response): void => {
    try {
      const { phoneNumber } = req.params;

      if (!phoneNumber) {
        res.status(400).json({
          success: false,
          message: 'Phone number is required'
        });
        return;
      }

      const history = this.conversationHistory.get(phoneNumber) || [];

      res.status(200).json({
        success: true,
        message: 'Conversation history retrieved successfully',
        data: {
          phoneNumber,
          messages: history,
          count: history.length
        }
      });
    } catch (error) {
      console.error('Error getting conversation history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve conversation history',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Clear conversation history for a phone number
   * DELETE /api/whatsapp/history/:phoneNumber
   */
  public clearConversationHistory = (req: Request, res: Response): void => {
    try {
      const { phoneNumber } = req.params;

      if (!phoneNumber) {
        res.status(400).json({
          success: false,
          message: 'Phone number is required'
        });
        return;
      }

      this.conversationHistory.delete(phoneNumber);

      res.status(200).json({
        success: true,
        message: 'Conversation history cleared successfully',
        data: { phoneNumber }
      });
    } catch (error) {
      console.error('Error clearing conversation history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear conversation history',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}
