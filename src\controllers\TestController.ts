import { Request, Response } from 'express';
import { AIService } from '@services/aiService';
import { VectorSearchService } from '@services/vectorSearchService';
import { dataLoader } from '@utils/dataLoader';
import { config } from '@config/environment';

/**
 * Test Controller
 * Provides endpoints for testing and validating the AI chatbot system
 */
export class TestController {
  private aiService: AIService;
  private vectorSearchService: VectorSearchService;

  constructor() {
    this.aiService = new AIService();
    this.vectorSearchService = new VectorSearchService();
  }

  /**
   * Test data loading functionality
   * GET /api/test/data-loading
   */
  public testDataLoading = async (req: Request, res: Response): Promise<void> => {
    try {
      const startTime = Date.now();
      
      // Test data loading
      const data = await dataLoader.getData();
      const stats = await dataLoader.getDataStats();
      
      const loadTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        message: 'Data loading test completed successfully',
        data: {
          loadTime: `${loadTime}ms`,
          totalItems: data.length,
          statistics: stats,
          sampleItems: data.slice(0, 3).map(item => ({
            id: item.id,
            type: item.metadata.type,
            content: item.content.substring(0, 100) + '...'
          }))
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Data loading test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Data loading test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Test vector search functionality
   * POST /api/test/vector-search
   */
  public testVectorSearch = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query = 'pizza' } = req.body;
      const startTime = Date.now();

      // Test different search methods
      const generalSearch = await this.vectorSearchService.search(query, 3);
      const restaurantSearch = await this.vectorSearchService.searchByType(query, 'restaurant', 3);
      const itemSearch = await this.vectorSearchService.searchByType(query, 'item', 3);
      const suggestions = await this.vectorSearchService.getSuggestions(query, 5);

      const searchTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        message: 'Vector search test completed successfully',
        data: {
          query,
          searchTime: `${searchTime}ms`,
          results: {
            general: {
              count: generalSearch.length,
              items: generalSearch.map(result => ({
                similarity: result.similarity,
                relevance: result.relevance,
                type: result.item.metadata.type,
                content: result.item.content.substring(0, 100) + '...'
              }))
            },
            restaurants: {
              count: restaurantSearch.length,
              items: restaurantSearch.map(result => ({
                similarity: result.similarity,
                content: result.item.content.substring(0, 100) + '...'
              }))
            },
            items: {
              count: itemSearch.length,
              items: itemSearch.map(result => ({
                similarity: result.similarity,
                content: result.item.content.substring(0, 100) + '...'
              }))
            },
            suggestions
          }
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Vector search test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Vector search test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Test AI service functionality
   * POST /api/test/ai-service
   */
  public testAIService = async (req: Request, res: Response): Promise<void> => {
    try {
      const { message = 'Hello, can you help me find some food?' } = req.body;
      const startTime = Date.now();

      // Test AI health check
      const isHealthy = await this.aiService.healthCheck();
      
      if (!isHealthy) {
        res.status(503).json({
          success: false,
          message: 'AI service is not healthy',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Test search and AI response
      const searchResults = await this.vectorSearchService.search(message, 3);
      const relevantData = searchResults.map(result => result.item);
      
      const aiResponse = await this.aiService.generateResponse(message, relevantData, []);
      
      const responseTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        message: 'AI service test completed successfully',
        data: {
          userMessage: message,
          responseTime: `${responseTime}ms`,
          aiHealthy: isHealthy,
          searchResults: {
            count: searchResults.length,
            items: searchResults.map(result => ({
              similarity: result.similarity,
              type: result.item.metadata.type,
              content: result.item.content.substring(0, 100) + '...'
            }))
          },
          aiResponse: aiResponse.substring(0, 200) + (aiResponse.length > 200 ? '...' : ''),
          fullResponse: aiResponse
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('AI service test failed:', error);
      res.status(500).json({
        success: false,
        message: 'AI service test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Test complete chatbot flow
   * POST /api/test/chatbot-flow
   */
  public testChatbotFlow = async (req: Request, res: Response): Promise<void> => {
    try {
      const { messages = ['Hi', 'I want pizza', 'What restaurants do you have?'] } = req.body;
      const startTime = Date.now();

      const conversationResults = [];

      for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        const messageStartTime = Date.now();

        // Search for relevant data
        const searchResults = await this.vectorSearchService.search(message, 3);
        const relevantData = searchResults.map(result => result.item);

        // Generate AI response
        const aiResponse = await this.aiService.generateResponse(message, relevantData, []);
        
        const messageTime = Date.now() - messageStartTime;

        conversationResults.push({
          step: i + 1,
          userMessage: message,
          searchResultsCount: searchResults.length,
          aiResponse: aiResponse.substring(0, 150) + (aiResponse.length > 150 ? '...' : ''),
          responseTime: `${messageTime}ms`
        });
      }

      const totalTime = Date.now() - startTime;

      res.status(200).json({
        success: true,
        message: 'Chatbot flow test completed successfully',
        data: {
          totalMessages: messages.length,
          totalTime: `${totalTime}ms`,
          averageResponseTime: `${Math.round(totalTime / messages.length)}ms`,
          conversation: conversationResults
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Chatbot flow test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Chatbot flow test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Test system configuration
   * GET /api/test/config
   */
  public testConfiguration = async (req: Request, res: Response): Promise<void> => {
    try {
      const configTest = {
        environment: config.nodeEnv,
        port: config.port,
        dataPath: config.data.vectorizedDataPath,
        aws: {
          region: config.aws.region,
          hasCredentials: !!(config.aws.accessKeyId && config.aws.secretAccessKey),
          modelId: config.aws.bedrock.modelId,
          embeddingModelId: config.aws.bedrock.embeddingModelId
        },
        whatsapp: {
          hasVerifyToken: !!config.whatsapp.verifyToken,
          hasAccessToken: !!config.whatsapp.accessToken,
          hasPhoneNumberId: !!config.whatsapp.phoneNumberId
        },
        ai: {
          maxTokens: config.ai.maxTokens,
          temperature: config.ai.temperature,
          vectorSearchLimit: config.ai.vectorSearchLimit,
          similarityThreshold: config.ai.similarityThreshold
        }
      };

      res.status(200).json({
        success: true,
        message: 'Configuration test completed successfully',
        data: configTest,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Configuration test failed:', error);
      res.status(500).json({
        success: false,
        message: 'Configuration test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Run all tests
   * GET /api/test/all
   */
  public runAllTests = async (req: Request, res: Response): Promise<void> => {
    try {
      const startTime = Date.now();
      const results: any = {};

      // Test data loading
      try {
        await dataLoader.getData();
        results.dataLoading = { status: 'passed', message: 'Data loaded successfully' };
      } catch (error) {
        results.dataLoading = { status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Test vector search
      try {
        const searchResults = await this.vectorSearchService.search('test', 1);
        results.vectorSearch = { status: 'passed', message: `Found ${searchResults.length} results` };
      } catch (error) {
        results.vectorSearch = { status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' };
      }

      // Test AI service
      try {
        const isHealthy = await this.aiService.healthCheck();
        results.aiService = { status: isHealthy ? 'passed' : 'failed', message: isHealthy ? 'AI service is healthy' : 'AI service is unhealthy' };
      } catch (error) {
        results.aiService = { status: 'failed', error: error instanceof Error ? error.message : 'Unknown error' };
      }

      const totalTime = Date.now() - startTime;
      const passedTests = Object.values(results).filter((result: any) => result.status === 'passed').length;
      const totalTests = Object.keys(results).length;

      res.status(200).json({
        success: passedTests === totalTests,
        message: `Test suite completed: ${passedTests}/${totalTests} tests passed`,
        data: {
          summary: {
            totalTests,
            passedTests,
            failedTests: totalTests - passedTests,
            totalTime: `${totalTime}ms`
          },
          results
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Test suite failed:', error);
      res.status(500).json({
        success: false,
        message: 'Test suite failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };
}
