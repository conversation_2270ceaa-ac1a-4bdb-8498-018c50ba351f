import { Router } from 'express';
import { DataController } from '@controllers/DataController';

/**
 * Data Routes
 * Handles data management and statistics endpoints
 */
const router = Router();
const dataController = new DataController();

// Get data statistics
router.get('/stats', dataController.getStats);

// Get data by type with pagination
router.get('/type/:type', dataController.getDataByType);

// Get data item by ID
router.get('/item/:id', dataController.getDataById);

// Search data items
router.post('/search', dataController.searchData);

// Reload data from file
router.post('/reload', dataController.reloadData);

// Get random data items for testing
router.get('/random', dataController.getRandomItems);

export default router;
