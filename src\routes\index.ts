import { Router, Request, Response } from 'express';
import { whatsAppRoutes } from './whatsapp';

/**
 * Main routes configuration
 */
export class Routes {
  public router: Router;

  constructor() {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Health check endpoint
    this.router.get('/health', (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: 'API is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env['NODE_ENV'] || 'development'
      });
    });

    // API info endpoint
    this.router.get('/', (_req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: 'Welcome to Cravin Concierge API',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/api/health',
          whatsapp: '/api/whatsapp/webhook'
        }
      });
    });

    // WhatsApp routes
    this.router.use('/whatsapp', whatsAppRoutes);
  }
}

// Export router instance
export const routes = new Routes().router;
