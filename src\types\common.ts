/**
 * Common types and interfaces used throughout the application
 */

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Vectorized Data Types
export interface VectorizedDataItem {
  id: string;
  content: string;
  metadata: {
    type: 'addon' | 'branch' | 'category' | 'item' | 'restaurant' | 'shop' | 'sports_landing';
    id: string;
    [key: string]: any;
  };
}

export interface VectorizedDataFile {
  timestamp: string;
  total_records: number;
  data: VectorizedDataItem[];
}

// AI and Chat Types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    whatsappMessageId?: string;
    phoneNumber?: string;
    [key: string]: any;
  };
}

export interface AIResponse {
  message: string;
  confidence: number;
  relevantData: VectorizedDataItem[];
  suggestions?: string[];
}

export interface SearchResult {
  item: VectorizedDataItem;
  similarity: number;
  relevance: number;
}

// WhatsApp Types
export interface WhatsAppMessage {
  id: string;
  from: string;
  timestamp: string;
  text?: {
    body: string;
  };
  type: 'text' | 'image' | 'audio' | 'video' | 'document';
}

export interface WhatsAppWebhookEntry {
  id: string;
  changes: Array<{
    value: {
      messaging_product: string;
      metadata: {
        display_phone_number: string;
        phone_number_id: string;
      };
      messages?: WhatsAppMessage[];
    };
    field: string;
  }>;
}

export interface WhatsAppWebhookPayload {
  object: string;
  entry: WhatsAppWebhookEntry[];
}
