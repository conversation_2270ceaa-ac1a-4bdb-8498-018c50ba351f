import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AIService } from '../services/aiService';
import { VectorSearchService } from '../services/vectorSearchService';
import { ChatMessage, AIResponse, ApiResponse } from '../types/common';
import { config } from '../config/environment';

/**
 * AI Controller
 * Handles conversational AI interactions and chat functionality
 */
export class AIController {
  private aiService: AIService;
  private vectorSearchService: VectorSearchService;
  private conversationHistory: Map<string, ChatMessage[]> = new Map();

  constructor() {
    this.aiService = new AIService();
    this.vectorSearchService = new VectorSearchService();
  }

  /**
   * Process chat message and generate AI response
   * POST /api/ai/chat
   */
  public chat = async (req: Request, res: Response): Promise<void> => {
    try {
      const { message, sessionId, phoneNumber } = req.body;

      if (!message || typeof message !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Message is required and must be a string',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const session = sessionId || phoneNumber || 'default';
      
      // Get or create conversation history
      let history = this.conversationHistory.get(session) || [];
      
      // Add user message to history
      const userMessage: ChatMessage = {
        id: uuidv4(),
        role: 'user',
        content: message,
        timestamp: new Date(),
        metadata: {
          phoneNumber,
          sessionId
        }
      };
      
      history.push(userMessage);

      // Search for relevant data
      const searchResults = await this.vectorSearchService.search(message);
      const relevantData = searchResults.map(result => result.item);

      // Generate AI response
      const aiResponseText = await this.aiService.generateResponse(
        message,
        relevantData,
        history.slice(-10) // Last 10 messages for context
      );

      // Create assistant message
      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: aiResponseText,
        timestamp: new Date(),
        metadata: {
          relevantDataCount: relevantData.length,
          searchResultsCount: searchResults.length
        }
      };

      history.push(assistantMessage);

      // Keep only last 20 messages to prevent memory issues
      if (history.length > 20) {
        history = history.slice(-20);
      }
      
      this.conversationHistory.set(session, history);

      // Get suggestions for follow-up
      const suggestions = await this.vectorSearchService.getSuggestions(message, 3);

      const response: AIResponse = {
        message: aiResponseText,
        confidence: this.calculateConfidence(searchResults),
        relevantData: relevantData.slice(0, 3), // Return top 3 for reference
        suggestions
      };

      res.status(200).json({
        success: true,
        message: 'Chat response generated successfully',
        data: response,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in chat endpoint:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process chat message',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Search for specific information
   * POST /api/ai/search
   */
  public search = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query, type, location, limit } = req.body;

      if (!query || typeof query !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Query is required and must be a string',
          timestamp: new Date().toISOString()
        });
        return;
      }

      let searchResults;

      if (location) {
        searchResults = await this.vectorSearchService.searchByLocation(query, location);
      } else if (type) {
        searchResults = await this.vectorSearchService.searchByType(query, type, limit);
      } else {
        searchResults = await this.vectorSearchService.search(query, limit);
      }

      res.status(200).json({
        success: true,
        message: 'Search completed successfully',
        data: {
          results: searchResults,
          query,
          type,
          location,
          count: searchResults.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in search endpoint:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to perform search',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get conversation history for a session
   * GET /api/ai/history/:sessionId
   */
  public getHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sessionId } = req.params;
      const history = this.conversationHistory.get(sessionId) || [];

      res.status(200).json({
        success: true,
        message: 'History retrieved successfully',
        data: {
          sessionId,
          messages: history,
          count: history.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve history',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Clear conversation history for a session
   * DELETE /api/ai/history/:sessionId
   */
  public clearHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { sessionId } = req.params;
      this.conversationHistory.delete(sessionId);

      res.status(200).json({
        success: true,
        message: 'History cleared successfully',
        data: { sessionId },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error clearing history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear history',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get AI service health status
   * GET /api/ai/health
   */
  public healthCheck = async (req: Request, res: Response): Promise<void> => {
    try {
      const isHealthy = await this.aiService.healthCheck();
      
      res.status(isHealthy ? 200 : 503).json({
        success: isHealthy,
        message: isHealthy ? 'AI service is healthy' : 'AI service is unhealthy',
        data: {
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error in health check:', error);
      res.status(503).json({
        success: false,
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get suggestions based on partial input
   * POST /api/ai/suggestions
   */
  public getSuggestions = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query, limit = 5 } = req.body;

      if (!query || typeof query !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Query is required and must be a string',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const suggestions = await this.vectorSearchService.getSuggestions(query, limit);

      res.status(200).json({
        success: true,
        message: 'Suggestions retrieved successfully',
        data: {
          query,
          suggestions,
          count: suggestions.length
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error getting suggestions:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get suggestions',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Calculate confidence score based on search results
   */
  private calculateConfidence(searchResults: any[]): number {
    if (searchResults.length === 0) {
      return 0.1;
    }

    const avgSimilarity = searchResults.reduce((sum, result) => sum + result.similarity, 0) / searchResults.length;
    const resultCountFactor = Math.min(searchResults.length / 5, 1); // Normalize to max 5 results
    
    return Math.min(avgSimilarity * resultCountFactor, 0.95);
  }
}
