import { VectorizedDataItem, SearchResult } from '@/types/common';
import { dataLoader } from '@utils/dataLoader';
import { AIService } from './aiService';
import { config } from '@config/environment';

/**
 * Vector Search Service
 * Handles semantic search and similarity matching for vectorized data
 */
export class VectorSearchService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  /**
   * Perform semantic search using text similarity
   */
  public async search(query: string, limit: number = config.ai.vectorSearchLimit): Promise<SearchResult[]> {
    try {
      const data = await dataLoader.getData();
      const queryKeywords = this.aiService.extractKeywords(query);
      
      // Calculate similarity scores for each item
      const results: SearchResult[] = data.map(item => {
        const contentSimilarity = this.aiService.calculateTextSimilarity(query, item.content);
        const metadataSimilarity = this.calculateMetadataSimilarity(queryKeywords, item);
        
        // Combine similarities with weights
        const similarity = (contentSimilarity * 0.7) + (metadataSimilarity * 0.3);
        
        return {
          item,
          similarity,
          relevance: this.calculateRelevance(query, item)
        };
      });

      // Filter by similarity threshold and sort
      return results
        .filter(result => result.similarity >= config.ai.similarityThreshold)
        .sort((a, b) => {
          // Primary sort by similarity, secondary by relevance
          if (Math.abs(a.similarity - b.similarity) < 0.1) {
            return b.relevance - a.relevance;
          }
          return b.similarity - a.similarity;
        })
        .slice(0, limit);
    } catch (error) {
      console.error('Error performing vector search:', error);
      throw new Error('Failed to perform search');
    }
  }

  /**
   * Search by specific type with enhanced filtering
   */
  public async searchByType(
    query: string, 
    type: VectorizedDataItem['metadata']['type'],
    limit: number = config.ai.vectorSearchLimit
  ): Promise<SearchResult[]> {
    try {
      const data = await dataLoader.getDataByType(type);
      const queryKeywords = this.aiService.extractKeywords(query);
      
      const results: SearchResult[] = data.map(item => {
        const contentSimilarity = this.aiService.calculateTextSimilarity(query, item.content);
        const metadataSimilarity = this.calculateMetadataSimilarity(queryKeywords, item);
        const typeBonusSimilarity = this.calculateTypeBonusSimilarity(query, item, type);
        
        // Combine similarities with weights (type bonus for more relevant results)
        const similarity = (contentSimilarity * 0.6) + (metadataSimilarity * 0.3) + (typeBonusSimilarity * 0.1);
        
        return {
          item,
          similarity,
          relevance: this.calculateRelevance(query, item)
        };
      });

      return results
        .filter(result => result.similarity >= (config.ai.similarityThreshold * 0.8)) // Lower threshold for type-specific search
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      console.error('Error performing type-specific search:', error);
      throw new Error('Failed to perform type-specific search');
    }
  }

  /**
   * Search for restaurants and their items
   */
  public async searchRestaurants(query: string): Promise<SearchResult[]> {
    try {
      const restaurantResults = await this.searchByType(query, 'restaurant', 3);
      const itemResults = await this.searchByType(query, 'item', 5);
      const addonResults = await this.searchByType(query, 'addon', 3);
      
      // Combine and re-sort results
      const allResults = [...restaurantResults, ...itemResults, ...addonResults];
      
      return allResults
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, config.ai.vectorSearchLimit);
    } catch (error) {
      console.error('Error searching restaurants:', error);
      throw new Error('Failed to search restaurants');
    }
  }

  /**
   * Search for items by location/branch
   */
  public async searchByLocation(query: string, location: string): Promise<SearchResult[]> {
    try {
      const data = await dataLoader.getData();
      const locationKeywords = this.aiService.extractKeywords(location.toLowerCase());
      
      // Filter by location first
      const locationFilteredData = data.filter(item => {
        const branchName = item.metadata.branch_name?.toLowerCase() || '';
        const branchId = item.metadata.branch_id?.toLowerCase() || '';
        
        return locationKeywords.some(keyword => 
          branchName.includes(keyword) || branchId.includes(keyword)
        );
      });

      if (locationFilteredData.length === 0) {
        return [];
      }

      // Then search within location-filtered data
      const queryKeywords = this.aiService.extractKeywords(query);
      
      const results: SearchResult[] = locationFilteredData.map(item => {
        const contentSimilarity = this.aiService.calculateTextSimilarity(query, item.content);
        const metadataSimilarity = this.calculateMetadataSimilarity(queryKeywords, item);
        
        const similarity = (contentSimilarity * 0.8) + (metadataSimilarity * 0.2);
        
        return {
          item,
          similarity,
          relevance: this.calculateRelevance(query, item)
        };
      });

      return results
        .filter(result => result.similarity >= (config.ai.similarityThreshold * 0.6))
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, config.ai.vectorSearchLimit);
    } catch (error) {
      console.error('Error searching by location:', error);
      throw new Error('Failed to search by location');
    }
  }

  /**
   * Calculate metadata similarity based on keywords
   */
  private calculateMetadataSimilarity(queryKeywords: string[], item: VectorizedDataItem): number {
    const metadataText = JSON.stringify(item.metadata).toLowerCase();
    const metadataKeywords = this.aiService.extractKeywords(metadataText);
    
    const matchingKeywords = queryKeywords.filter(keyword => 
      metadataKeywords.some(metaKeyword => 
        metaKeyword.includes(keyword) || keyword.includes(metaKeyword)
      )
    );
    
    return queryKeywords.length > 0 ? matchingKeywords.length / queryKeywords.length : 0;
  }

  /**
   * Calculate type-specific bonus similarity
   */
  private calculateTypeBonusSimilarity(
    query: string, 
    item: VectorizedDataItem, 
    searchType: VectorizedDataItem['metadata']['type']
  ): number {
    const queryLower = query.toLowerCase();
    
    // Type-specific keyword bonuses
    const typeKeywords: Record<string, string[]> = {
      'item': ['food', 'dish', 'meal', 'eat', 'order', 'menu'],
      'addon': ['extra', 'add', 'side', 'additional', 'topping'],
      'restaurant': ['restaurant', 'place', 'location', 'branch'],
      'branch': ['branch', 'location', 'address', 'where'],
      'category': ['category', 'type', 'kind', 'section'],
      'shop': ['shop', 'store', 'outlet'],
      'sports_landing': ['sports', 'game', 'play', 'activity']
    };

    const keywords = typeKeywords[searchType] || [];
    const matchingKeywords = keywords.filter(keyword => queryLower.includes(keyword));
    
    return keywords.length > 0 ? matchingKeywords.length / keywords.length : 0;
  }

  /**
   * Calculate relevance score based on various factors
   */
  private calculateRelevance(query: string, item: VectorizedDataItem): number {
    let relevance = 0;
    const queryLower = query.toLowerCase();
    const contentLower = item.content.toLowerCase();
    
    // Exact matches get higher relevance
    if (contentLower.includes(queryLower)) {
      relevance += 0.5;
    }
    
    // Price information adds relevance for price-related queries
    if (queryLower.includes('price') || queryLower.includes('cost') || queryLower.includes('aed')) {
      if (item.metadata.price) {
        relevance += 0.3;
      }
    }
    
    // Location queries get bonus for items with location info
    if (queryLower.includes('where') || queryLower.includes('location') || queryLower.includes('branch')) {
      if (item.metadata.branch_name) {
        relevance += 0.3;
      }
    }
    
    // Popular item types get slight bonus
    if (item.metadata.type === 'item' || item.metadata.type === 'restaurant') {
      relevance += 0.1;
    }
    
    return Math.min(relevance, 1.0); // Cap at 1.0
  }

  /**
   * Get suggestions based on partial query
   */
  public async getSuggestions(partialQuery: string, limit: number = 5): Promise<string[]> {
    try {
      if (partialQuery.length < 2) {
        return [];
      }

      const data = await dataLoader.getData();
      const suggestions = new Set<string>();
      
      data.forEach(item => {
        const content = item.content.toLowerCase();
        const query = partialQuery.toLowerCase();
        
        if (content.includes(query)) {
          // Extract relevant words from content
          const words = content.split(/\s+/);
          words.forEach(word => {
            if (word.includes(query) && word.length > 2) {
              suggestions.add(word.charAt(0).toUpperCase() + word.slice(1));
            }
          });
        }
        
        // Add metadata suggestions
        if (item.metadata.name && item.metadata.name.toLowerCase().includes(query)) {
          suggestions.add(item.metadata.name);
        }
      });
      
      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      console.error('Error getting suggestions:', error);
      return [];
    }
  }
}
