import { Router } from 'express';
import { <PERSON><PERSON>ontroller } from '@controllers/AIController';

/**
 * AI Routes
 * Handles conversational AI and search endpoints
 */
const router = Router();
const aiController = new AIController();

// Chat endpoint for conversational AI
router.post('/chat', aiController.chat);

// Search endpoint for finding specific information
router.post('/search', aiController.search);

// Get conversation history
router.get('/history/:sessionId', aiController.getHistory);

// Clear conversation history
router.delete('/history/:sessionId', aiController.clearHistory);

// Get suggestions based on partial input
router.post('/suggestions', aiController.getSuggestions);

// Health check for AI service
router.get('/health', aiController.healthCheck);

export default router;
