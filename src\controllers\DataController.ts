import { Request, Response } from 'express';
import { dataLoader } from '@utils/dataLoader';
import { VectorSearchService } from '@services/vectorSearchService';

/**
 * Data Controller
 * Handles data management and statistics endpoints
 */
export class DataController {
  private vectorSearchService: VectorSearchService;

  constructor() {
    this.vectorSearchService = new VectorSearchService();
  }

  /**
   * Get data statistics
   * GET /api/data/stats
   */
  public getStats = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await dataLoader.getDataStats();

      res.status(200).json({
        success: true,
        message: 'Data statistics retrieved successfully',
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting data stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve data statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get data by type
   * GET /api/data/type/:type
   */
  public getDataByType = async (req: Request, res: Response): Promise<void> => {
    try {
      const { type } = req.params;
      const { limit = 10, offset = 0 } = req.query;

      if (!type) {
        res.status(400).json({
          success: false,
          message: 'Type parameter is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const validTypes = ['addon', 'branch', 'category', 'item', 'restaurant', 'shop', 'sports_landing'];
      if (!validTypes.includes(type)) {
        res.status(400).json({
          success: false,
          message: `Invalid type. Valid types are: ${validTypes.join(', ')}`,
          timestamp: new Date().toISOString()
        });
        return;
      }

      const data = await dataLoader.getDataByType(type as any);
      const limitNum = parseInt(limit as string, 10) || 10;
      const offsetNum = parseInt(offset as string, 10) || 0;
      
      const paginatedData = data.slice(offsetNum, offsetNum + limitNum);

      res.status(200).json({
        success: true,
        message: `Data of type '${type}' retrieved successfully`,
        data: {
          items: paginatedData,
          pagination: {
            total: data.length,
            limit: limitNum,
            offset: offsetNum,
            hasMore: offsetNum + limitNum < data.length
          }
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting data by type:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve data by type',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get data item by ID
   * GET /api/data/item/:id
   */
  public getDataById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      if (!id) {
        res.status(400).json({
          success: false,
          message: 'ID parameter is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const item = await dataLoader.getDataById(id);

      if (!item) {
        res.status(404).json({
          success: false,
          message: 'Data item not found',
          timestamp: new Date().toISOString()
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Data item retrieved successfully',
        data: item,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting data by ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve data item',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Search data items
   * POST /api/data/search
   */
  public searchData = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query, type, location, limit = 10 } = req.body;

      if (!query || typeof query !== 'string') {
        res.status(400).json({
          success: false,
          message: 'Query is required and must be a string',
          timestamp: new Date().toISOString()
        });
        return;
      }

      let searchResults;

      if (location) {
        searchResults = await this.vectorSearchService.searchByLocation(query, location);
      } else if (type) {
        searchResults = await this.vectorSearchService.searchByType(query, type, limit);
      } else {
        searchResults = await this.vectorSearchService.search(query, limit);
      }

      res.status(200).json({
        success: true,
        message: 'Search completed successfully',
        data: {
          query,
          results: searchResults,
          count: searchResults.length,
          filters: {
            type: type || null,
            location: location || null
          }
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error searching data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to search data',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Reload data from file
   * POST /api/data/reload
   */
  public reloadData = async (req: Request, res: Response): Promise<void> => {
    try {
      const data = await dataLoader.reloadData();

      res.status(200).json({
        success: true,
        message: 'Data reloaded successfully',
        data: {
          itemsLoaded: data.length,
          reloadedAt: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error reloading data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reload data',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  /**
   * Get random data items for testing
   * GET /api/data/random
   */
  public getRandomItems = async (req: Request, res: Response): Promise<void> => {
    try {
      const { count = 5 } = req.query;
      const countNum = Math.min(parseInt(count as string, 10) || 5, 20); // Max 20 items

      const items = await dataLoader.getRandomItems(countNum);

      res.status(200).json({
        success: true,
        message: 'Random data items retrieved successfully',
        data: {
          items,
          count: items.length,
          requested: countNum
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error getting random items:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve random items',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };
}
