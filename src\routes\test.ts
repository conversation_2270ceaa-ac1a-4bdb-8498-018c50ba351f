import { Router } from 'express';
import { TestController } from '@controllers/TestController';

/**
 * Test Routes
 * Provides endpoints for testing and validating the AI chatbot system
 */
const router = Router();
const testController = new TestController();

// Test data loading functionality
router.get('/data-loading', testController.testDataLoading);

// Test vector search functionality
router.post('/vector-search', testController.testVectorSearch);

// Test AI service functionality
router.post('/ai-service', testController.testAIService);

// Test complete chatbot flow
router.post('/chatbot-flow', testController.testChatbotFlow);

// Test system configuration
router.get('/config', testController.testConfiguration);

// Run all tests
router.get('/all', testController.runAllTests);

export default router;
