import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Environment configuration
 */
export const config = {
  // Server configuration
  port: parseInt(process.env['PORT'] || '3000', 10),
  host: process.env['HOST'] || 'localhost',
  nodeEnv: process.env['NODE_ENV'] || 'development',

  // API configuration
  apiPrefix: process.env['API_PREFIX'] || '/api',
  apiVersion: process.env['API_VERSION'] || 'v1',

  // Security configuration
  corsOrigins: process.env['CORS_ORIGINS']?.split(',') || ['http://localhost:3000'],
  rateLimitWindow: parseInt(process.env['RATE_LIMIT_WINDOW'] || '900000', 10), // 15 minutes
  rateLimitMax: parseInt(process.env['RATE_LIMIT_MAX'] || '100', 10),

  // Database configuration (for future use)
  database: {
    url: process.env['DATABASE_URL'] || '',
    host: process.env['DB_HOST'] || 'localhost',
    port: parseInt(process.env['DB_PORT'] || '5432', 10),
    name: process.env['DB_NAME'] || 'cravin_concierge',
    username: process.env['DB_USERNAME'] || process.env['DB_USER'] || '',
    password: process.env['DB_PASSWORD'] || '',
  },



  // Logging configuration
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    format: process.env['LOG_FORMAT'] || 'combined',
  },

  // Application configuration
  app: {
    name: process.env['APP_NAME'] || 'Cravin Concierge API',
    version: process.env['APP_VERSION'] || '1.0.0',
    description: process.env['APP_DESCRIPTION'] || 'TypeScript Express.js MVC API',
  },

  // WhatsApp configuration
  whatsapp: {
    verifyToken: process.env['WHATSAPP_VERIFY_TOKEN'],
    accessToken: process.env['WHATSAPP_ACCESS_TOKEN'],
    phoneNumberId: process.env['WHATSAPP_PHONE_NUMBER_ID']
  },

  // AWS Bedrock configuration
  aws: {
    region: process.env['AWS_REGION'] || 'us-east-1',
    accessKeyId: process.env['AWS_ACCESS_KEY_ID'],
    secretAccessKey: process.env['AWS_SECRET_ACCESS_KEY'],
    bedrock: {
      modelId: process.env['AWS_BEDROCK_MODEL_ID'] || 'anthropic.claude-3-sonnet-20240229-v1:0',
      embeddingModelId: process.env['AWS_BEDROCK_EMBEDDING_MODEL_ID'] || 'amazon.titan-embed-text-v1'
    }
  },

  // AI configuration
  ai: {
    maxTokens: parseInt(process.env['AI_MAX_TOKENS'] || '1000', 10),
    temperature: parseFloat(process.env['AI_TEMPERATURE'] || '0.7'),
    topK: parseInt(process.env['AI_TOP_K'] || '250', 10),
    topP: parseFloat(process.env['AI_TOP_P'] || '0.999'),
    vectorSearchLimit: parseInt(process.env['VECTOR_SEARCH_LIMIT'] || '5', 10),
    similarityThreshold: parseFloat(process.env['SIMILARITY_THRESHOLD'] || '0.7')
  },

  // Data configuration
  data: {
    vectorizedDataPath: process.env['VECTORIZED_DATA_PATH'] || './vectorized_data.json'
  }
};

/**
 * Validate required environment variables
 */
export const validateConfig = (): void => {
  const requiredVars: string[] = [
    // Add required environment variables here
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
};

/**
 * Check if running in development mode
 */
export const isDevelopment = (): boolean => {
  return config.nodeEnv === 'development';
};

/**
 * Check if running in production mode
 */
export const isProduction = (): boolean => {
  return config.nodeEnv === 'production';
};

/**
 * Check if running in test mode
 */
export const isTest = (): boolean => {
  return config.nodeEnv === 'test';
};
