# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# API Configuration
API_PREFIX=/api
API_VERSION=v1

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Database Configuration (for future use)
DATABASE_URL=
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cravin_concierge
DB_USERNAME=
DB_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# Application Configuration
APP_NAME=Cravin Concierge API
APP_VERSION=1.0.0
APP_DESCRIPTION=TypeScript Express.js MVC API with AI Chatbot

# WhatsApp Configuration
WHATSAPP_VERIFY_TOKEN=your_whatsapp_verify_token_here
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here

# AWS Bedrock Configuration
AWS_BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
AWS_BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v1

# AI Configuration
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.7
AI_TOP_K=250
AI_TOP_P=0.999
VECTOR_SEARCH_LIMIT=5
SIMILARITY_THRESHOLD=0.7

# Data Configuration
VECTORIZED_DATA_PATH=./vectorized_data.json
