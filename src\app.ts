import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { config, validateConfig } from './config/environment';
import { routes } from './routes/index';
import { initDatabase } from './config/database';

import {
  getLogger,
  requestId
} from './middleware/logger';
import {
  apiLimiter,
  corsOptions,
  securityHeaders,
  contentSecurityPolicy
} from './middleware/security';

/**
 * Express application class
 */
export class App {
  public app: Application;
  public port: number;

  constructor() {
    this.app = express();
    this.port = config.port;

    // Validate configuration
    this.validateEnvironment();

    // Initialize middleware and routes
    this.initializeMiddleware();
    this.initializeRoutes();
  }

  /**
   * Initialize the application with async operations
   */
  public async initialize(): Promise<void> {
    // Initialize database
    await this.initializeDatabase();
  }

  /**
   * Validate environment configuration
   */
  private validateEnvironment(): void {
    try {
      validateConfig();
      console.log('✅ Environment configuration validated');
    } catch (error) {
      console.error('❌ Environment validation failed:', error);
      process.exit(1);
    }
  }

  /**
   * Initialize database connection
   */
  private async initializeDatabase(): Promise<void> {
    try {
      await initDatabase();
      console.log('✅ Database connection initialized');
    } catch (error) {
      console.error('❌ Database initialization failed:', error);
      process.exit(1);
    }
  }

  /**
   * Initialize middleware
   */
  private initializeMiddleware(): void {
    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', 1);

    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false // We'll handle CSP manually
    }));
    this.app.use(securityHeaders);
    this.app.use(contentSecurityPolicy);

    // CORS middleware
    this.app.use(cors(corsOptions));

    // Rate limiting
    this.app.use(apiLimiter);

    // Compression middleware
    this.app.use(compression());

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request tracking middleware
    this.app.use(requestId);

    // Logging middleware
    this.app.use(getLogger());

    console.log('✅ Middleware initialized');
  }

  /**
   * Initialize routes
   */
  private initializeRoutes(): void {
    // Mount API routes
    this.app.use(config.apiPrefix, routes);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: `Welcome to ${config.app.name}`,
        version: config.app.version,
        description: config.app.description,
        timestamp: new Date().toISOString(),
        environment: config.nodeEnv,
        apiEndpoint: `${req.protocol}://${req.get('host')}${config.apiPrefix}`
      });
    });

    console.log('✅ Routes initialized');
    console.log('✅ Application initialized');
  }



  /**
   * Start the server
   */
  public listen(): void {
    this.app.listen(this.port, config.host, () => {
      console.log('\n🚀 Server started successfully!');
      console.log(`📍 Server running at: http://${config.host}:${this.port}`);
      console.log(`🌐 API endpoint: http://${config.host}:${this.port}${config.apiPrefix}`);
      console.log(`🔧 Environment: ${config.nodeEnv}`);
      console.log(`📊 Process ID: ${process.pid}`);
      console.log(`⏰ Started at: ${new Date().toISOString()}\n`);
    });

    // Graceful shutdown handling
    this.setupGracefulShutdown();
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      // Close server
      const server = this.app.listen();
      server.close(async () => {
        console.log('✅ HTTP server closed');

        // Close database connections
        try {
          const { pool } = require('./config/database');
          await pool.end();
          console.log('✅ Database connections closed');
        } catch (error) {
          console.error('❌ Error closing database connections:', error);
        }

        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      });

      // Force close after 10 seconds
      setTimeout(() => {
        console.error('❌ Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 10000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  /**
   * Get Express application instance
   */
  public getApp(): Application {
    return this.app;
  }
}
